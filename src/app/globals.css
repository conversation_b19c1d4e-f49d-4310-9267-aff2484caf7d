@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
    :root {
      /* Light mode - cream background theme */
      --background: 60 9% 98%; /* #FBFBF9 */
      --foreground: 220 13% 18%; /* Dark gray for text */

      --card: 0 0% 100%; /* White cards */
      --card-foreground: 220 13% 18%;

      --popover: 0 0% 100%;
      --popover-foreground: 220 13% 18%;

      --primary: 221 83% 53%; /* Blue #3B82F6 */
      --primary-foreground: 0 0% 98%;

      --secondary: 47 96% 53%; /* Yellow #EAB308 */
      --secondary-foreground: 220 13% 18%;

      --muted: 220 13% 95%; /* Light gray */
      --muted-foreground: 220 13% 45%;

      --accent: 262 83% 58%; /* Purple #8B5CF6 */
      --accent-foreground: 0 0% 98%;

      --destructive: 0 84.2% 60.2%;
      --destructive-foreground: 0 0% 98%;

      --border: 220 13% 85%; /* Light border */
      --input: 220 13% 91%;
      --ring: 221 83% 53%;

      --radius: 0.5rem;
    }

    .dark {
      /* Dark warm background */
      --background: 220 13% 9%; /* Dark warm gray */
      --foreground: 60 9% 98%; /* Cream text */

      --card: 220 13% 12%; /* Dark cards */
      --card-foreground: 60 9% 98%;

      --popover: 220 13% 12%;
      --popover-foreground: 60 9% 98%;

      --primary: 221 83% 53%; /* Blue */
      --primary-foreground: 220 13% 9%;

      --secondary: 47 96% 53%; /* Yellow */
      --secondary-foreground: 220 13% 9%;

      --muted: 220 13% 18%;
      --muted-foreground: 220 13% 65%;

      --accent: 262 83% 58%; /* Purple */
      --accent-foreground: 220 13% 9%;

      --destructive: 0 62.8% 30.6%;
      --destructive-foreground: 0 0% 98%;

      --border: 220 13% 18%;
      --input: 220 13% 18%;
      --ring: 221 83% 53%;
    }
  }

  @layer base {
    * {
      @apply border-border;
    }
    body {
      @apply bg-background text-foreground;
    }
  }

  h3 code {
    @apply !text-lg md:!text-xl;
  }
  
  pre {
    @apply !px-0 rounded-lg overflow-x-auto py-4
  }
   
  pre [data-line] {
    @apply px-4
  }

  code {
    @apply text-sm md:text-base !leading-loose;
  }
  
  pre > code {
    counter-reset: line;
  }
  
  code[data-theme*=" "],
  code[data-theme*=" "] span {
    color: var(--shiki-light);
    background-color: var(--shiki-light-bg);
  }
   
  @media (prefers-color-scheme: dark) {
    code[data-theme*=" "],
    code[data-theme*=" "] span {
      color: var(--shiki-dark);
      background-color: var(--shiki-dark-bg);
    }
  }
  
  code[data-line-numbers] {
    counter-reset: line;
  }
  
  code[data-line-numbers] > [data-line]::before {
    counter-increment: line;
    content: counter(line);
    @apply inline-block w-4 mr-4 text-right text-gray-500;
  }
 
  code {
    counter-reset: line;
  }
 
  code > [data-line]::before {
  counter-increment: line;
  content: counter(line);
 
  /* Other styling */
  display: inline-block;
  width: 1rem;
  margin-right: 2rem;
  text-align: right;
  color: gray;
}
 
code[data-line-numbers-max-digits="2"] > [data-line]::before {
  width: 2rem;
}
 
code[data-line-numbers-max-digits="3"] > [data-line]::before {
  width: 3rem;
}

/* Badge improvements */
.badge-minimal {
  @apply inline-flex items-center rounded-md px-3 py-1 text-xs font-medium;
  @apply bg-muted/50 text-muted-foreground border border-border/50;
  @apply hover:bg-muted/70 transition-colors;
}

/* Better card spacing */
.card-spacing {
  @apply space-y-6;
}

.section-container {
  @apply max-w-6xl mx-auto px-4 sm:px-6 lg:px-8;
}